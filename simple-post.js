const axios = require('axios');

// Configuration
const API_TOKEN = 'your-api-token-here'; // Replace with your actual API token

// Simple POST request function
async function simplePost() {
  try {
    // Simple payload
    const payload = {
      
      email: '<EMAIL>',
     
    };

    console.log('Sending POST request with payload:');
    console.log(JSON.stringify(payload, null, 2));

    // Make POST request to the API endpoint
    const response = await axios.post('https://umanage.dev.hidglobal.com/api/api-token', payload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}` // Add API token to headers
      }
    });

    console.log('\n--- Response Data ---');
    
    console.log(response.data);

  } catch (error) {
    console.error('\n--- Error occurred ---');
    console.error('Error message:', error.message);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    }
  }
}

// Execute the function
console.log('Starting simple POST request...\n');
simplePost();
